from fastapi import APIRouter
from app.api.endpoints import auth, user, qrcode, share, health, exercise, chat
from app.api.endpoints import food_router, meal_router, team_router
from app.api.endpoints import food_recognition
from app.api.endpoints import llm_logs
from app.api.endpoints import nutrient
from app.api.endpoints import training_plan
from app.api.endpoints.community import router as community_router
from app.api.endpoints import workout
from app.api.endpoints import ai_chat
from app.api.endpoints import ai_food_recognition
from app.api.endpoints import video_processing
from app.api.endpoints.gamification import router as gamification_router
# from app.api.v1.endpoints import team

api_router = APIRouter()

# 认证相关
api_router.include_router(auth.router, prefix="/auth", tags=["auth"])

# 健康检查
api_router.include_router(health.router, tags=["health"])

# 用户相关
api_router.include_router(user.router, prefix="/user", tags=["users"])

# 二维码生成
api_router.include_router(qrcode.router, prefix="/qrcode", tags=["qrcode"])

# 分享追踪
api_router.include_router(share.router, prefix="/share", tags=["share"])

# 健身动作
api_router.include_router(exercise.router, prefix="/exercise", tags=["exercise"])

# 食品库
api_router.include_router(food_router, prefix="/food", tags=["food"])

# 餐食记录
api_router.include_router(meal_router, prefix="/meal", tags=["meal"])

# 食物识别 - 只保留连字符版本
api_router.include_router(food_recognition.router, prefix="/food-recognition", tags=["food-recognition"])

# 大模型调用日志 - 仅限管理员访问
api_router.include_router(llm_logs.router, prefix="/llm-logs", tags=["llm-logs"])

# 聊天功能
api_router.include_router(chat.router, prefix="/chat", tags=["chat"])

# 营养素推荐
api_router.include_router(nutrient.router, prefix="/nutrient", tags=["nutrient"])

# 训练计划
api_router.include_router(training_plan.router, prefix="/training-plan", tags=["training-plan"])

# 训练日管理 - 同时支持单数和复数形式
api_router.include_router(workout.router, prefix="/workout", tags=["workout"])
# 添加复数形式的路由，保持向后兼容性
api_router.include_router(workout.router, prefix="/workouts", tags=["workout"])

# 社区功能
api_router.include_router(community_router, prefix="/community", tags=["community"])

# 团队功能
api_router.include_router(team_router, prefix="/team", tags=["team"])

# 添加重构后的AI助手聊天路由
api_router.include_router(ai_chat.router, prefix="/ai-assistant", tags=["ai-assistant"])

# 添加AI食物识别接口
api_router.include_router(ai_food_recognition.router, prefix="/ai-food", tags=["ai-food-recognition"])

# 添加视频处理接口
api_router.include_router(video_processing.router, prefix="/video", tags=["video-processing"])

# 游戏化系统接口
api_router.include_router(gamification_router, prefix="/gamification", tags=["gamification"])