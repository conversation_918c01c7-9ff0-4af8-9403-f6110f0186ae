"""
LangGraph统一状态定义

定义智能健身AI助手系统的统一状态模型，继承MessagesState并包含所有必要的状态字段。
"""

import logging
from typing import Annotated, Dict, Any, List, Optional, Union
from datetime import datetime
from langgraph.graph import MessagesState
from langgraph.graph.message import add_messages
from langchain_core.messages import AnyMessage, BaseMessage

logger = logging.getLogger(__name__)

class UnifiedFitnessState(MessagesState):
    """
    统一的健身AI助手状态定义
    
    继承自MessagesState，包含健身AI助手所需的所有状态字段，
    确保与现有ConversationState的兼容性。
    """
    
    # ===== 基础会话信息 =====
    conversation_id: str = ""
    user_id: str = ""
    session_id: str = ""
    timestamp: Optional[datetime] = None
    
    # ===== 意图识别结果（多层级） =====
    intent: str = ""
    confidence: float = 0.0
    intent_parameters: Dict[str, Any] = {}
    enhanced_intent_result: Optional[Dict[str, Any]] = None
    original_intent: Optional[str] = None  # 原始识别的意图
    
    # ===== 用户信息 =====
    user_profile: Dict[str, Any] = {}
    user_preferences: Dict[str, Any] = {}
    user_context: Dict[str, Any] = {}
    
    # ===== 训练参数和健身数据 =====
    training_params: Dict[str, Any] = {}
    fitness_goals: List[str] = []
    current_workout: Optional[Dict[str, Any]] = None
    exercise_history: List[Dict[str, Any]] = []
    
    # ===== 流程状态和控制 =====
    flow_state: Dict[str, Any] = {}
    current_state_name: str = "idle"
    previous_state_name: str = ""
    state_transition_history: List[str] = []
    
    # ===== 系统状态和路由信息 =====
    current_node: str = ""
    processing_system: str = ""  # "enhanced", "legacy", "state_machine", "hybrid", "langgraph"
    processing_path: List[str] = []  # 处理路径追踪
    routing_decision: Dict[str, Any] = {}
    
    # ===== 响应信息 =====
    response_content: str = ""
    response_type: str = "text"  # "text", "structured", "streaming"
    structured_data: Dict[str, Any] = {}
    response_metadata: Dict[str, Any] = {}
    
    # ===== 错误处理和重试 =====
    error_count: int = 0
    last_error: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    # ===== 性能指标 =====
    processing_start_time: Optional[float] = None
    processing_end_time: Optional[float] = None
    node_execution_times: Dict[str, float] = {}
    total_processing_time: float = 0.0
    
    # ===== LangGraph特定字段 =====
    graph_execution_id: Optional[str] = None
    checkpoint_id: Optional[str] = None
    parallel_results: List[Dict[str, Any]] = []  # 并行处理结果
    selected_result: Optional[Dict[str, Any]] = None  # 选中的最佳结果
    
    # ===== 上下文和历史 =====
    conversation_history: List[Dict[str, Any]] = []
    context_summary: str = ""
    long_term_memory: Dict[str, Any] = {}
    
    # ===== 配置和控制标志 =====
    enable_streaming: bool = True
    enable_parallel_processing: bool = True
    enable_human_in_loop: bool = False
    debug_mode: bool = False
    
    # ===== 消息历史（自动管理） =====
    messages: Annotated[List[AnyMessage], add_messages] = []
    
    def __post_init__(self):
        """状态初始化后的处理"""
        if not self.timestamp:
            self.timestamp = datetime.now()
        
        if not self.graph_execution_id:
            import uuid
            self.graph_execution_id = str(uuid.uuid4())
    
    def to_conversation_state_dict(self) -> Dict[str, Any]:
        """
        转换为ConversationState兼容的字典格式
        
        Returns:
            与现有ConversationState兼容的字典
        """
        return {
            "conversation_id": self.conversation_id,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "current_state": self.current_state_name,
            "intent": self.intent,
            "confidence": self.confidence,
            "user_profile": self.user_profile,
            "training_params": self.training_params,
            "flow_state": self.flow_state,
            "response_content": self.response_content,
            "structured_data": self.structured_data,
            "conversation_history": self.conversation_history,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None
        }
    
    @classmethod
    def from_conversation_state(cls, conv_state: Dict[str, Any]) -> "UnifiedFitnessState":
        """
        从ConversationState字典创建UnifiedFitnessState
        
        Args:
            conv_state: ConversationState字典
            
        Returns:
            UnifiedFitnessState实例
        """
        state = cls()
        
        # 映射基础字段
        state.conversation_id = conv_state.get("conversation_id", "")
        state.user_id = conv_state.get("user_id", "")
        state.session_id = conv_state.get("session_id", "")
        state.current_state_name = conv_state.get("current_state", "idle")
        state.intent = conv_state.get("intent", "")
        state.confidence = conv_state.get("confidence", 0.0)
        state.user_profile = conv_state.get("user_profile", {})
        state.training_params = conv_state.get("training_params", {})
        state.flow_state = conv_state.get("flow_state", {})
        state.response_content = conv_state.get("response_content", "")
        state.structured_data = conv_state.get("structured_data", {})
        state.conversation_history = conv_state.get("conversation_history", [])
        
        # 处理时间戳
        timestamp_str = conv_state.get("timestamp")
        if timestamp_str:
            try:
                state.timestamp = datetime.fromisoformat(timestamp_str)
            except (ValueError, TypeError):
                state.timestamp = datetime.now()
        else:
            state.timestamp = datetime.now()
        
        return state
    
    def update_processing_metrics(self, node_name: str, execution_time: float):
        """
        更新处理指标
        
        Args:
            node_name: 节点名称
            execution_time: 执行时间
        """
        self.node_execution_times[node_name] = execution_time
        self.total_processing_time += execution_time
        
        if not self.processing_start_time:
            self.processing_start_time = datetime.now().timestamp()
        
        self.processing_end_time = datetime.now().timestamp()
    
    def add_processing_step(self, step: str):
        """
        添加处理步骤到路径追踪
        
        Args:
            step: 处理步骤描述
        """
        self.processing_path.append(step)
        logger.debug(f"处理路径: {' -> '.join(self.processing_path)}")
    
    def set_error(self, error_message: str, increment_count: bool = True):
        """
        设置错误信息
        
        Args:
            error_message: 错误消息
            increment_count: 是否增加错误计数
        """
        self.last_error = error_message
        if increment_count:
            self.error_count += 1
        
        logger.error(f"状态错误 (#{self.error_count}): {error_message}")
    
    def can_retry(self) -> bool:
        """
        检查是否可以重试
        
        Returns:
            是否可以重试
        """
        return self.retry_count < self.max_retries
    
    def increment_retry(self):
        """增加重试计数"""
        self.retry_count += 1
        logger.info(f"重试计数: {self.retry_count}/{self.max_retries}")
    
    def reset_error_state(self):
        """重置错误状态"""
        self.last_error = None
        self.retry_count = 0
        logger.debug("错误状态已重置")
    
    def add_parallel_result(self, result: Dict[str, Any]):
        """
        添加并行处理结果
        
        Args:
            result: 处理结果
        """
        self.parallel_results.append(result)
        logger.debug(f"添加并行结果，当前结果数: {len(self.parallel_results)}")
    
    def clear_parallel_results(self):
        """清空并行处理结果"""
        self.parallel_results.clear()
        self.selected_result = None
        logger.debug("并行处理结果已清空")
    
    def get_summary(self) -> Dict[str, Any]:
        """
        获取状态摘要
        
        Returns:
            状态摘要字典
        """
        return {
            "conversation_id": self.conversation_id,
            "user_id": self.user_id,
            "current_state": self.current_state_name,
            "current_node": self.current_node,
            "intent": self.intent,
            "confidence": self.confidence,
            "processing_system": self.processing_system,
            "processing_path": self.processing_path,
            "error_count": self.error_count,
            "retry_count": self.retry_count,
            "total_processing_time": self.total_processing_time,
            "parallel_results_count": len(self.parallel_results),
            "messages_count": len(self.messages),
            "timestamp": self.timestamp.isoformat() if self.timestamp else None
        }
    
    def validate(self) -> bool:
        """
        验证状态的有效性
        
        Returns:
            状态是否有效
        """
        try:
            # 检查必需字段
            if not self.conversation_id:
                logger.error("conversation_id不能为空")
                return False
            
            if not self.user_id:
                logger.error("user_id不能为空")
                return False
            
            # 检查置信度范围
            if not (0.0 <= self.confidence <= 1.0):
                logger.error(f"confidence必须在0-1范围内: {self.confidence}")
                return False
            
            # 检查重试计数
            if self.retry_count < 0 or self.retry_count > self.max_retries:
                logger.error(f"retry_count超出范围: {self.retry_count}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"状态验证失败: {str(e)}")
            return False
