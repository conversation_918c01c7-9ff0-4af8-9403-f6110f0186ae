"""
训练模型重构数据迁移脚本
将 TrainingTemplate 重命名为 WorkoutTemplate，并将数据从 UserTrainingPlanRecord 迁移到 WorkoutExercise + SetRecord
"""

from sqlalchemy.orm import Session
from sqlalchemy import text
from datetime import datetime, date
import json
import logging

from app.db.session import SessionLocal
from app.models.training_template import WorkoutTemplate
from app.models.workout_exercise import WorkoutExercise
from app.models.set_record import SetRecord
from app.models.user_training_plan_record import UserTrainingPlanRecord
from app.models.exercise import Exercise

logger = logging.getLogger(__name__)

def migrate_table_structure(db: Session):
    """执行表结构迁移"""
    try:
        logger.info("开始表结构迁移...")

        # 1. 重命名表
        db.execute(text("ALTER TABLE training_templates RENAME TO workout_templates;"))

        # 2. 更新字段
        db.execute(text("ALTER TABLE workout_templates RENAME COLUMN template_name TO name;"))
        db.execute(text("ALTER TABLE workout_templates ADD COLUMN IF NOT EXISTS description TEXT;"))
        db.execute(text("ALTER TABLE workout_templates ADD COLUMN IF NOT EXISTS estimated_duration SMALLINT;"))
        db.execute(text("ALTER TABLE workout_templates ADD COLUMN IF NOT EXISTS target_body_parts INTEGER[];"))
        db.execute(text("ALTER TABLE workout_templates ADD COLUMN IF NOT EXISTS training_scenario VARCHAR(20);"))

        # 3. 添加新的关联字段到 workout_exercises
        db.execute(text("ALTER TABLE workout_exercises ADD COLUMN IF NOT EXISTS template_id INTEGER REFERENCES workout_templates(id);"))

        # 4. 创建索引
        db.execute(text("CREATE INDEX IF NOT EXISTS idx_workout_exercises_template_id ON workout_exercises(template_id);"))

        db.commit()
        logger.info("表结构迁移完成")

    except Exception as e:
        db.rollback()
        logger.error(f"表结构迁移失败: {str(e)}")
        raise

def migrate_training_templates_to_workout_exercises(db: Session):
    """将 TrainingTemplate 的 JSON exercises 转换为 WorkoutExercise 记录"""
    try:
        logger.info("开始迁移训练模板数据...")

        # 查询所有模板（使用原始SQL，因为模型已经改变）
        result = db.execute(text("SELECT id, user_id, exercises FROM workout_templates WHERE exercises IS NOT NULL;"))
        templates = result.fetchall()

        migrated_count = 0

        for template_row in templates:
            template_id, user_id, exercises_json = template_row

            if not exercises_json:
                continue

            try:
                exercises = json.loads(exercises_json) if isinstance(exercises_json, str) else exercises_json

                for idx, exercise_data in enumerate(exercises):
                    exercise_id = exercise_data.get('exerciseId') or exercise_data.get('exercise_id')

                    if not exercise_id:
                        logger.warning(f"模板 {template_id} 中的动作缺少 exercise_id，跳过")
                        continue

                    # 检查动作是否存在
                    exercise_exists = db.execute(
                        text("SELECT 1 FROM exercises WHERE id = :exercise_id"),
                        {"exercise_id": exercise_id}
                    ).fetchone()

                    if not exercise_exists:
                        logger.warning(f"动作 {exercise_id} 不存在，跳过")
                        continue

                    # 创建 WorkoutExercise 记录
                    workout_exercise = WorkoutExercise(
                        template_id=template_id,
                        exercise_id=exercise_id,
                        sets=exercise_data.get('sets', 3),
                        reps=str(exercise_data.get('reps', '10')),
                        weight=exercise_data.get('weight'),
                        rest_seconds=exercise_data.get('rest_seconds', 60),
                        order=idx + 1,
                        notes=exercise_data.get('notes'),
                        exercise_type=exercise_data.get('exercise_type', 'weight_reps')
                    )
                    db.add(workout_exercise)
                    migrated_count += 1

            except (json.JSONDecodeError, TypeError) as e:
                logger.error(f"解析模板 {template_id} 的 exercises JSON 失败: {str(e)}")
                continue

        # 清空原来的 exercises JSON 字段
        db.execute(text("UPDATE workout_templates SET exercises = NULL;"))

        db.commit()
        logger.info(f"训练模板数据迁移完成，共迁移 {migrated_count} 个动作")

    except Exception as e:
        db.rollback()
        logger.error(f"训练模板数据迁移失败: {str(e)}")
        raise

def migrate_user_training_records_to_set_records(db: Session):
    """将 UserTrainingPlanRecord 迁移到 WorkoutExercise + SetRecord"""
    try:
        logger.info("开始迁移用户训练记录...")

        # 查询所有用户训练记录
        old_records = db.query(UserTrainingPlanRecord).all()
        migrated_count = 0

        for record in old_records:
            try:
                # 查找或创建对应的 WorkoutExercise
                workout_exercise = db.query(WorkoutExercise).filter(
                    WorkoutExercise.exercise_id == record.exercise_id,
                    WorkoutExercise.template_id.is_(None),
                    WorkoutExercise.workout_id.is_(None),
                    WorkoutExercise.daily_workout_id.is_(None)
                ).first()

                if not workout_exercise:
                    # 创建一个独立的 WorkoutExercise 用于历史记录
                    workout_exercise = WorkoutExercise(
                        exercise_id=record.exercise_id,
                        sets=record.total_sets or 3,
                        reps="10"  # 默认值
                    )
                    db.add(workout_exercise)
                    db.flush()  # 获取 ID

                # 迁移 sets JSON 数据到 SetRecord
                if record.sets:
                    sets_data = record.sets if isinstance(record.sets, list) else []

                    for set_idx, set_data in enumerate(sets_data):
                        if isinstance(set_data, dict):
                            set_record = SetRecord(
                                workout_exercise_id=workout_exercise.id,
                                set_number=set_idx + 1,
                                weight=float(set_data.get('weight', 0)) if set_data.get('weight') else None,
                                reps=int(set_data.get('reps', 0)) if set_data.get('reps') else None,
                                completed=set_data.get('completed', False),
                                notes=set_data.get('notes'),
                                created_at=record.created_at or datetime.utcnow(),
                                updated_at=record.updated_at or datetime.utcnow()
                            )
                            db.add(set_record)
                            migrated_count += 1

            except Exception as e:
                logger.error(f"迁移记录 {record.id} 失败: {str(e)}")
                continue

        db.commit()
        logger.info(f"用户训练记录迁移完成，共迁移 {migrated_count} 个组记录")

    except Exception as e:
        db.rollback()
        logger.error(f"用户训练记录迁移失败: {str(e)}")
        raise

def run_migration():
    """运行完整的数据迁移"""
    db = SessionLocal()
    try:
        logger.info("开始训练模型重构数据迁移...")

        # 阶段1：表结构迁移
        migrate_table_structure(db)

        # 阶段2：训练模板数据迁移
        migrate_training_templates_to_workout_exercises(db)

        # 阶段3：用户训练记录迁移
        migrate_user_training_records_to_set_records(db)

        logger.info("所有数据迁移完成！")

    except Exception as e:
        logger.error(f"数据迁移失败: {str(e)}")
        raise
    finally:
        db.close()

def rollback_migration():
    """回滚迁移（仅结构部分）"""
    db = SessionLocal()
    try:
        logger.info("开始回滚迁移...")

        # 删除新增的字段
        db.execute(text("ALTER TABLE workout_exercises DROP COLUMN IF EXISTS template_id;"))
        db.execute(text("ALTER TABLE workout_templates DROP COLUMN IF EXISTS description;"))
        db.execute(text("ALTER TABLE workout_templates DROP COLUMN IF EXISTS estimated_duration;"))
        db.execute(text("ALTER TABLE workout_templates DROP COLUMN IF EXISTS target_body_parts;"))
        db.execute(text("ALTER TABLE workout_templates DROP COLUMN IF EXISTS training_scenario;"))

        # 重命名回原来的表名和字段名
        db.execute(text("ALTER TABLE workout_templates RENAME COLUMN name TO template_name;"))
        db.execute(text("ALTER TABLE workout_templates RENAME TO training_templates;"))

        db.commit()
        logger.info("迁移回滚完成")

    except Exception as e:
        db.rollback()
        logger.error(f"迁移回滚失败: {str(e)}")
        raise
    finally:
        db.close()

if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(level=logging.INFO)

    # 运行迁移
    run_migration()
